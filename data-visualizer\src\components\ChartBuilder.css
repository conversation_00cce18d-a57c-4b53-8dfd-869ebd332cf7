.chart-builder {
  display: flex;
  gap: 24px;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.chart-builder-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #6b7280;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 40px 20px;
}

.chart-builder-empty svg {
  margin-bottom: 24px;
  opacity: 0.5;
}

.chart-builder-empty h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
}

.chart-builder-empty p {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
  max-width: 400px;
}

.chart-builder-sidebar {
  width: 320px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
  overflow-y: auto;
  height: fit-content;
  max-height: 100%;
}

.chart-builder-main {
  flex: 1;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
  overflow: auto;
}

.config-section {
  margin-bottom: 32px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.config-item input,
.config-item select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
}

.config-item input:focus,
.config-item select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.chart-type-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.chart-type-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
}

.chart-type-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.chart-type-btn.active {
  border-color: #4f46e5;
  background: #f8faff;
  color: #4f46e5;
}

.chart-icon {
  font-size: 24px;
}

.chart-name {
  font-weight: 500;
  text-align: center;
}

.color-scheme-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-scheme-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
}

.color-scheme-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.color-scheme-btn.active {
  border-color: #4f46e5;
  background: #f8faff;
}

.color-preview {
  display: flex;
  gap: 2px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.scheme-name {
  font-weight: 500;
  color: #374151;
}

.dimension-controls {
  display: flex;
  gap: 12px;
}

.dimension-input {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dimension-input label {
  font-size: 12px;
  color: #6b7280;
}

.dimension-input input {
  padding: 6px 8px;
  font-size: 13px;
}

.chart-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
}

.config-item input[type="range"] {
  width: 100%;
  margin: 8px 0;
}

.range-value {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  display: block;
}

@media (max-width: 1024px) {
  .chart-builder {
    flex-direction: column;
    height: auto;
  }
  
  .chart-builder-sidebar {
    width: 100%;
    max-height: none;
  }
  
  .chart-type-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .chart-type-btn {
    padding: 12px 8px;
  }
  
  .chart-icon {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .chart-builder {
    gap: 16px;
  }
  
  .chart-builder-sidebar,
  .chart-builder-main {
    padding: 16px;
  }
  
  .chart-type-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .dimension-controls {
    flex-direction: column;
    gap: 8px;
  }
}
