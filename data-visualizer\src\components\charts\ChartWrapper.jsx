import React from 'react';
import Base<PERSON><PERSON> from './BaseChart';
import <PERSON><PERSON><PERSON> from './PieChart';
import './ChartWrapper.css';

const ChartWrapper = ({ 
  data,
  chartType = 'bar',
  xColumn,
  yColumn,
  labelColumn,
  valueColumn,
  width,
  height,
  colorScheme = 'category10',
  onDataPointClick,
  className = '',
  title,
  showTitle = true
}) => {
  if (!data || !data.length) {
    return (
      <div className={`chart-wrapper empty ${className}`}>
        <div className="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="20" x2="18" y2="10" />
            <line x1="12" y1="20" x2="12" y2="4" />
            <line x1="6" y1="20" x2="6" y2="14" />
          </svg>
          <p>No data available</p>
          <span>Please select data columns to create a chart</span>
        </div>
      </div>
    );
  }

  const renderChart = () => {
    switch (chartType) {
      case 'pie':
      case 'donut':
        return (
          <PieChart
            data={data}
            labelColumn={labelColumn}
            valueColumn={valueColumn}
            width={width}
            height={height}
            colorScheme={colorScheme}
            onDataPointClick={onDataPointClick}
            className={className}
            showLabels={true}
            showLegend={true}
          />
        );
      
      case 'bar':
      case 'line':
      case 'scatter':
      case 'area':
        return (
          <BaseChart
            data={data}
            chartType={chartType}
            xColumn={xColumn}
            yColumn={yColumn}
            width={width}
            height={height}
            colorScheme={colorScheme}
            onDataPointClick={onDataPointClick}
            className={className}
          />
        );
      
      default:
        return (
          <div className="chart-error">
            <p>Unsupported chart type: {chartType}</p>
          </div>
        );
    }
  };

  return (
    <div className={`chart-wrapper ${className}`}>
      {showTitle && title && (
        <div className="chart-title">
          <h3>{title}</h3>
        </div>
      )}
      <div className="chart-content">
        {renderChart()}
      </div>
    </div>
  );
};

// Helper function to get available chart types based on data
export const getAvailableChartTypes = (data, columns) => {
  if (!data || !columns || columns.length === 0) return [];

  const numericColumns = columns.filter(col => {
    const sample = data.slice(0, 100);
    const values = sample.map(row => row[col]).filter(val => val !== null && val !== undefined);
    const numericValues = values.filter(val => !isNaN(val) && val !== '');
    return numericValues.length / values.length > 0.8;
  });

  const categoricalColumns = columns.filter(col => !numericColumns.includes(col));

  const chartTypes = [];

  // Bar chart: categorical x, numeric y
  if (categoricalColumns.length > 0 && numericColumns.length > 0) {
    chartTypes.push({ type: 'bar', name: 'Bar Chart', icon: '📊' });
  }

  // Line chart: numeric x, numeric y (or time series)
  if (numericColumns.length >= 2) {
    chartTypes.push({ type: 'line', name: 'Line Chart', icon: '📈' });
  }

  // Scatter plot: numeric x, numeric y
  if (numericColumns.length >= 2) {
    chartTypes.push({ type: 'scatter', name: 'Scatter Plot', icon: '🔵' });
  }

  // Area chart: similar to line chart
  if (numericColumns.length >= 2) {
    chartTypes.push({ type: 'area', name: 'Area Chart', icon: '🏔️' });
  }

  // Pie chart: categorical label, numeric value
  if (categoricalColumns.length > 0 && numericColumns.length > 0) {
    chartTypes.push({ type: 'pie', name: 'Pie Chart', icon: '🥧' });
  }

  return chartTypes;
};

// Helper function to suggest column mappings
export const suggestColumnMappings = (data, columns, chartType) => {
  if (!data || !columns || columns.length === 0) return {};

  const numericColumns = columns.filter(col => {
    const sample = data.slice(0, 100);
    const values = sample.map(row => row[col]).filter(val => val !== null && val !== undefined);
    const numericValues = values.filter(val => !isNaN(val) && val !== '');
    return numericValues.length / values.length > 0.8;
  });

  const categoricalColumns = columns.filter(col => !numericColumns.includes(col));

  const suggestions = {};

  switch (chartType) {
    case 'bar':
      suggestions.xColumn = categoricalColumns[0] || columns[0];
      suggestions.yColumn = numericColumns[0] || columns[1];
      break;
    
    case 'line':
    case 'scatter':
    case 'area':
      suggestions.xColumn = numericColumns[0] || columns[0];
      suggestions.yColumn = numericColumns[1] || numericColumns[0] || columns[1];
      break;
    
    case 'pie':
      suggestions.labelColumn = categoricalColumns[0] || columns[0];
      suggestions.valueColumn = numericColumns[0] || columns[1];
      break;
  }

  return suggestions;
};

export default ChartWrapper;
