import React, { useState } from 'react';
import { useData } from '../contexts/DataContext';
import './DataPreview.css';

const DataPreview = () => {
  const { currentDataset, datasets, setCurrentDataset, removeDataset } = useData();
  const [showFullData, setShowFullData] = useState(false);

  if (!currentDataset) {
    return (
      <div className="data-preview-empty">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
          <line x1="9" y1="9" x2="15" y2="9" />
          <line x1="9" y1="15" x2="15" y2="15" />
        </svg>
        <p>No data loaded</p>
        <span>Upload a CSV file or connect to an API to get started</span>
      </div>
    );
  }

  const displayData = showFullData ? currentDataset.data : currentDataset.data.slice(0, 10);
  const hasMoreData = currentDataset.data.length > 10;

  const getColumnType = (column) => {
    const sample = currentDataset.data.slice(0, 100);
    const values = sample.map(row => row[column]).filter(val => val !== null && val !== undefined);
    
    if (values.length === 0) return 'text';
    
    const numericValues = values.filter(val => !isNaN(val) && val !== '');
    const dateValues = values.filter(val => !isNaN(Date.parse(val)));
    
    if (numericValues.length / values.length > 0.8) return 'number';
    if (dateValues.length / values.length > 0.8) return 'date';
    return 'text';
  };

  return (
    <div className="data-preview">
      <div className="data-preview-header">
        <div className="dataset-info">
          <h3>{currentDataset.name}</h3>
          <div className="dataset-meta">
            <span className="data-count">{currentDataset.data.length} rows</span>
            <span className="column-count">{currentDataset.columns.length} columns</span>
            <span className="data-type">{currentDataset.type.toUpperCase()}</span>
          </div>
        </div>
        
        {datasets.length > 1 && (
          <div className="dataset-selector">
            <select 
              value={currentDataset.id} 
              onChange={(e) => {
                const dataset = datasets.find(d => d.id === e.target.value);
                setCurrentDataset(dataset);
              }}
            >
              {datasets.map(dataset => (
                <option key={dataset.id} value={dataset.id}>
                  {dataset.name}
                </option>
              ))}
            </select>
          </div>
        )}
        
        <button 
          className="remove-dataset-btn"
          onClick={() => removeDataset(currentDataset.id)}
          title="Remove dataset"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline points="3,6 5,6 21,6" />
            <path d="m19,6v14a2,2 0,0 1,-2,2H7a2,2 0,0 1,-2,-2V6m3,0V4a2,2 0,0 1,2,-2h4a2,2 0,0 1,2,2v2" />
          </svg>
        </button>
      </div>

      <div className="columns-overview">
        <h4>Columns ({currentDataset.columns.length})</h4>
        <div className="columns-list">
          {currentDataset.columns.map(column => (
            <div key={column} className="column-item">
              <span className="column-name">{column}</span>
              <span className={`column-type ${getColumnType(column)}`}>
                {getColumnType(column)}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="data-table-container">
        <div className="data-table-header">
          <h4>Data Preview</h4>
          {hasMoreData && (
            <button 
              className="toggle-view-btn"
              onClick={() => setShowFullData(!showFullData)}
            >
              {showFullData ? 'Show Less' : `Show All (${currentDataset.data.length} rows)`}
            </button>
          )}
        </div>
        
        <div className="data-table-wrapper">
          <table className="data-table">
            <thead>
              <tr>
                {currentDataset.columns.map(column => (
                  <th key={column}>{column}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {displayData.map((row, index) => (
                <tr key={index}>
                  {currentDataset.columns.map(column => (
                    <td key={column}>
                      {row[column] !== null && row[column] !== undefined 
                        ? String(row[column]) 
                        : '-'
                      }
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {!showFullData && hasMoreData && (
          <div className="table-footer">
            Showing 10 of {currentDataset.data.length} rows
          </div>
        )}
      </div>
    </div>
  );
};

export default DataPreview;
