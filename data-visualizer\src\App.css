.app {
  min-height: 100vh;
  background: #f8fafc;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  text-align: center;
}

.header-content h1 {
  margin: 0 0 12px 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.app-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 20px;
  display: flex;
  gap: 0;
  overflow-x: auto;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
}

.nav-btn:hover {
  color: #374151;
  background: #f9fafb;
}

.nav-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
  background: #f8faff;
}

.nav-btn svg {
  width: 20px;
  height: 20px;
}

.app-main {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-content {
  width: 100%;
}

.tab-content {
  animation: fadeIn 0.3s ease-in;
}

.tab-content h2 {
  margin: 0 0 32px 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #111827;
  text-align: center;
}

.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  color: #6b7280;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.coming-soon svg {
  margin-bottom: 24px;
  opacity: 0.5;
}

.coming-soon p {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.coming-soon span {
  font-size: 16px;
  color: #6b7280;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .header-content h1 {
    font-size: 2rem;
  }

  .header-content p {
    font-size: 1rem;
    padding: 0 20px;
  }

  .app-nav {
    padding: 0 10px;
  }

  .nav-btn {
    padding: 14px 16px;
    font-size: 13px;
  }

  .app-main {
    padding: 20px 10px;
  }

  .tab-content h2 {
    font-size: 1.5rem;
    margin-bottom: 24px;
  }
}
