import React, { useState } from 'react';
import { useData } from '../contexts/DataContext';
import './ApiConnection.css';

const ApiConnection = () => {
  const { connectToAPI, loading, error, clearError } = useData();
  const [apiConfig, setApiConfig] = useState({
    name: '',
    url: '',
    method: 'GET',
    headers: {},
    refreshInterval: 0
  });
  const [headerInput, setHeaderInput] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    clearError();
    
    try {
      // Parse headers from string input
      const headers = {};
      if (headerInput.trim()) {
        const headerLines = headerInput.split('\n');
        headerLines.forEach(line => {
          const [key, value] = line.split(':').map(s => s.trim());
          if (key && value) {
            headers[key] = value;
          }
        });
      }

      const config = {
        ...apiConfig,
        headers
      };

      await connectToAPI(config);
      
      // Reset form on success
      setApiConfig({
        name: '',
        url: '',
        method: 'GET',
        headers: {},
        refreshInterval: 0
      });
      setHeaderInput('');
    } catch (error) {
      console.error('API connection failed:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setApiConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const sampleAPIs = [
    {
      name: 'JSONPlaceholder Posts',
      url: 'https://jsonplaceholder.typicode.com/posts',
      description: 'Sample blog posts data'
    },
    {
      name: 'JSONPlaceholder Users',
      url: 'https://jsonplaceholder.typicode.com/users',
      description: 'Sample user data'
    },
    {
      name: 'REST Countries',
      url: 'https://restcountries.com/v3.1/all',
      description: 'Country information'
    },
    {
      name: 'CoinGecko Crypto',
      url: 'https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=10&page=1',
      description: 'Cryptocurrency market data'
    }
  ];

  const loadSampleAPI = (sample) => {
    setApiConfig(prev => ({
      ...prev,
      name: sample.name,
      url: sample.url
    }));
  };

  return (
    <div className="api-connection">
      <div className="api-connection-header">
        <h3>Connect to API</h3>
        <p>Fetch data from REST APIs to create dynamic visualizations</p>
      </div>

      <div className="api-connection-content">
        <div className="sample-apis">
          <h4>Try Sample APIs</h4>
          <div className="sample-api-grid">
            {sampleAPIs.map((sample, index) => (
              <div key={index} className="sample-api-card">
                <div className="sample-api-info">
                  <h5>{sample.name}</h5>
                  <p>{sample.description}</p>
                  <code>{sample.url}</code>
                </div>
                <button
                  className="load-sample-btn"
                  onClick={() => loadSampleAPI(sample)}
                >
                  Load
                </button>
              </div>
            ))}
          </div>
        </div>

        <div className="api-form-container">
          <form onSubmit={handleSubmit} className="api-form">
            <div className="form-group">
              <label>Connection Name</label>
              <input
                type="text"
                value={apiConfig.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., Sales Data API"
                required
              />
            </div>

            <div className="form-group">
              <label>API URL</label>
              <input
                type="url"
                value={apiConfig.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
                placeholder="https://api.example.com/data"
                required
              />
            </div>

            <div className="form-group">
              <label>HTTP Method</label>
              <select
                value={apiConfig.method}
                onChange={(e) => handleInputChange('method', e.target.value)}
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
              </select>
            </div>

            <div className="advanced-toggle">
              <button
                type="button"
                className="toggle-btn"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <polyline points={showAdvanced ? "6,9 12,15 18,9" : "9,18 15,12 9,6"} />
                </svg>
                Advanced Options
              </button>
            </div>

            {showAdvanced && (
              <div className="advanced-options">
                <div className="form-group">
                  <label>Headers (one per line, format: Key: Value)</label>
                  <textarea
                    value={headerInput}
                    onChange={(e) => setHeaderInput(e.target.value)}
                    placeholder="Authorization: Bearer your-token&#10;Content-Type: application/json"
                    rows="4"
                  />
                </div>

                <div className="form-group">
                  <label>Auto-refresh Interval (seconds, 0 = disabled)</label>
                  <input
                    type="number"
                    value={apiConfig.refreshInterval}
                    onChange={(e) => handleInputChange('refreshInterval', parseInt(e.target.value) || 0)}
                    min="0"
                    max="3600"
                    placeholder="0"
                  />
                </div>
              </div>
            )}

            {error && (
              <div className="error-message">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10" />
                  <line x1="15" y1="9" x2="9" y2="15" />
                  <line x1="9" y1="9" x2="15" y2="15" />
                </svg>
                <span>{error}</span>
              </div>
            )}

            <button
              type="submit"
              className="connect-btn"
              disabled={loading || !apiConfig.name || !apiConfig.url}
            >
              {loading ? (
                <>
                  <div className="spinner"></div>
                  Connecting...
                </>
              ) : (
                <>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                  </svg>
                  Connect to API
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ApiConnection;
