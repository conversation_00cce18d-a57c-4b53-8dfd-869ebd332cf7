.chart-wrapper {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.chart-wrapper.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #6b7280;
  padding: 40px 20px;
}

.empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.empty-state span {
  font-size: 14px;
  color: #6b7280;
}

.chart-title {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.chart-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.chart-content {
  padding: 20px;
}

.chart-container {
  width: 100%;
  overflow: hidden;
}

.chart-container svg {
  width: 100%;
  height: auto;
  display: block;
}

.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #dc2626;
  font-size: 16px;
  text-align: center;
}

/* Chart-specific styles */
.pie-chart {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .chart-content {
    padding: 15px;
  }
  
  .chart-title {
    padding: 12px 15px;
  }
  
  .chart-title h3 {
    font-size: 16px;
  }
  
  .empty-state {
    padding: 30px 15px;
  }
  
  .empty-state p {
    font-size: 16px;
  }
}

/* Animation for chart transitions */
.chart-container {
  transition: all 0.3s ease;
}

/* Tooltip styles for D3 charts */
.chart-container .tooltip {
  pointer-events: none;
}

.chart-container .tooltip rect {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Hover effects for chart elements */
.chart-container .bar:hover,
.chart-container .dot:hover,
.chart-container .arc path:hover {
  filter: brightness(1.1);
}

/* Legend styles */
.legend {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.legend-item {
  cursor: default;
}

.legend-item:hover rect {
  filter: brightness(1.1);
}

/* Axis styles */
.chart-container .x-axis,
.chart-container .y-axis {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.chart-container .x-axis line,
.chart-container .y-axis line,
.chart-container .x-axis path,
.chart-container .y-axis path {
  stroke: #e5e7eb;
}

.chart-container .x-label,
.chart-container .y-label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}
