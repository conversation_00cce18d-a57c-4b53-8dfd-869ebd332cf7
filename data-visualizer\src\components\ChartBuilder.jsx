import React, { useState, useEffect } from 'react';
import { useData } from '../contexts/DataContext';
import ChartWrapper, { getAvailableChartTypes, suggestColumnMappings } from './charts/ChartWrapper';
import './ChartBuilder.css';

const ChartBuilder = () => {
  const { currentDataset } = useData();
  const [chartConfig, setChartConfig] = useState({
    type: 'bar',
    xColumn: '',
    yColumn: '',
    labelColumn: '',
    valueColumn: '',
    title: '',
    colorScheme: 'category10',
    width: 800,
    height: 400,
    showGrid: true,
    showLegend: true,
    showLabels: true,
    animationDuration: 750,
    margin: { top: 20, right: 30, bottom: 40, left: 50 }
  });

  const [availableChartTypes, setAvailableChartTypes] = useState([]);

  useEffect(() => {
    if (currentDataset) {
      const chartTypes = getAvailableChartTypes(currentDataset.data, currentDataset.columns);
      setAvailableChartTypes(chartTypes);
      
      if (chartTypes.length > 0) {
        const firstType = chartTypes[0].type;
        const suggestions = suggestColumnMappings(currentDataset.data, currentDataset.columns, firstType);
        
        setChartConfig(prev => ({
          ...prev,
          type: firstType,
          title: `${currentDataset.name} - ${chartTypes[0].name}`,
          ...suggestions
        }));
      }
    }
  }, [currentDataset]);

  const handleChartTypeChange = (newType) => {
    if (currentDataset) {
      const suggestions = suggestColumnMappings(currentDataset.data, currentDataset.columns, newType);
      const chartTypeName = availableChartTypes.find(ct => ct.type === newType)?.name || newType;
      
      setChartConfig(prev => ({
        ...prev,
        type: newType,
        title: `${currentDataset.name} - ${chartTypeName}`,
        ...suggestions
      }));
    }
  };

  const handleConfigChange = (key, value) => {
    setChartConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const colorSchemes = [
    { value: 'category10', name: 'Category 10', colors: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'] },
    { value: 'Accent', name: 'Accent', colors: ['#7fc97f', '#beaed4', '#fdc086', '#ffff99'] },
    { value: 'Dark2', name: 'Dark 2', colors: ['#1b9e77', '#d95f02', '#7570b3', '#e7298a'] },
    { value: 'Paired', name: 'Paired', colors: ['#a6cee3', '#1f78b4', '#b2df8a', '#33a02c'] },
    { value: 'Set1', name: 'Set 1', colors: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3'] },
    { value: 'Set2', name: 'Set 2', colors: ['#66c2a5', '#fc8d62', '#8da0cb', '#e78ac3'] },
    { value: 'Set3', name: 'Set 3', colors: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072'] }
  ];

  if (!currentDataset) {
    return (
      <div className="chart-builder-empty">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <line x1="18" y1="20" x2="18" y2="10" />
          <line x1="12" y1="20" x2="12" y2="4" />
          <line x1="6" y1="20" x2="6" y2="14" />
        </svg>
        <h3>No Data Available</h3>
        <p>Please upload a CSV file or connect to an API to start creating charts.</p>
      </div>
    );
  }

  const isPieChart = chartConfig.type === 'pie' || chartConfig.type === 'donut';

  return (
    <div className="chart-builder">
      <div className="chart-builder-sidebar">
        <div className="config-section">
          <h3>Chart Type</h3>
          <div className="chart-type-grid">
            {availableChartTypes.map(chartType => (
              <button
                key={chartType.type}
                className={`chart-type-btn ${chartConfig.type === chartType.type ? 'active' : ''}`}
                onClick={() => handleChartTypeChange(chartType.type)}
              >
                <span className="chart-icon">{chartType.icon}</span>
                <span className="chart-name">{chartType.name}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="config-section">
          <h3>Data Mapping</h3>
          <div className="config-group">
            {!isPieChart ? (
              <>
                <div className="config-item">
                  <label>X-Axis Column</label>
                  <select
                    value={chartConfig.xColumn}
                    onChange={(e) => handleConfigChange('xColumn', e.target.value)}
                  >
                    <option value="">Select column...</option>
                    {currentDataset.columns.map(column => (
                      <option key={column} value={column}>{column}</option>
                    ))}
                  </select>
                </div>
                <div className="config-item">
                  <label>Y-Axis Column</label>
                  <select
                    value={chartConfig.yColumn}
                    onChange={(e) => handleConfigChange('yColumn', e.target.value)}
                  >
                    <option value="">Select column...</option>
                    {currentDataset.columns.map(column => (
                      <option key={column} value={column}>{column}</option>
                    ))}
                  </select>
                </div>
              </>
            ) : (
              <>
                <div className="config-item">
                  <label>Label Column</label>
                  <select
                    value={chartConfig.labelColumn}
                    onChange={(e) => handleConfigChange('labelColumn', e.target.value)}
                  >
                    <option value="">Select column...</option>
                    {currentDataset.columns.map(column => (
                      <option key={column} value={column}>{column}</option>
                    ))}
                  </select>
                </div>
                <div className="config-item">
                  <label>Value Column</label>
                  <select
                    value={chartConfig.valueColumn}
                    onChange={(e) => handleConfigChange('valueColumn', e.target.value)}
                  >
                    <option value="">Select column...</option>
                    {currentDataset.columns.map(column => (
                      <option key={column} value={column}>{column}</option>
                    ))}
                  </select>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="config-section">
          <h3>Appearance</h3>
          <div className="config-group">
            <div className="config-item">
              <label>Chart Title</label>
              <input
                type="text"
                value={chartConfig.title}
                onChange={(e) => handleConfigChange('title', e.target.value)}
                placeholder="Enter chart title..."
              />
            </div>
            
            <div className="config-item">
              <label>Color Scheme</label>
              <div className="color-scheme-grid">
                {colorSchemes.map(scheme => (
                  <button
                    key={scheme.value}
                    className={`color-scheme-btn ${chartConfig.colorScheme === scheme.value ? 'active' : ''}`}
                    onClick={() => handleConfigChange('colorScheme', scheme.value)}
                    title={scheme.name}
                  >
                    <div className="color-preview">
                      {scheme.colors.map((color, index) => (
                        <div
                          key={index}
                          className="color-dot"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <span className="scheme-name">{scheme.name}</span>
                  </button>
                ))}
              </div>
            </div>

            <div className="config-item">
              <label>Dimensions</label>
              <div className="dimension-controls">
                <div className="dimension-input">
                  <label>Width</label>
                  <input
                    type="number"
                    value={chartConfig.width}
                    onChange={(e) => handleConfigChange('width', parseInt(e.target.value))}
                    min="300"
                    max="1200"
                    step="50"
                  />
                </div>
                <div className="dimension-input">
                  <label>Height</label>
                  <input
                    type="number"
                    value={chartConfig.height}
                    onChange={(e) => handleConfigChange('height', parseInt(e.target.value))}
                    min="200"
                    max="800"
                    step="50"
                  />
                </div>
              </div>
            </div>

            <div className="config-item">
              <label>Display Options</label>
              <div className="checkbox-group">
                <label className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={chartConfig.showGrid}
                    onChange={(e) => handleConfigChange('showGrid', e.target.checked)}
                  />
                  <span>Show Grid</span>
                </label>
                <label className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={chartConfig.showLegend}
                    onChange={(e) => handleConfigChange('showLegend', e.target.checked)}
                  />
                  <span>Show Legend</span>
                </label>
                <label className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={chartConfig.showLabels}
                    onChange={(e) => handleConfigChange('showLabels', e.target.checked)}
                  />
                  <span>Show Labels</span>
                </label>
              </div>
            </div>

            <div className="config-item">
              <label>Animation Duration (ms)</label>
              <input
                type="range"
                min="0"
                max="2000"
                step="100"
                value={chartConfig.animationDuration}
                onChange={(e) => handleConfigChange('animationDuration', parseInt(e.target.value))}
              />
              <span className="range-value">{chartConfig.animationDuration}ms</span>
            </div>
          </div>
        </div>
      </div>

      <div className="chart-builder-main">
        <div className="chart-preview">
          <ChartWrapper
            data={currentDataset.data}
            chartType={chartConfig.type}
            xColumn={chartConfig.xColumn}
            yColumn={chartConfig.yColumn}
            labelColumn={chartConfig.labelColumn}
            valueColumn={chartConfig.valueColumn}
            width={chartConfig.width}
            height={chartConfig.height}
            colorScheme={chartConfig.colorScheme}
            title={chartConfig.title}
            showTitle={true}
            onDataPointClick={(dataPoint) => {
              console.log('Data point clicked:', dataPoint);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ChartBuilder;
