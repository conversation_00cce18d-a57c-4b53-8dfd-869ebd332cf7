{"name": "@types/d3-transition", "version": "3.0.9", "description": "TypeScript definitions for d3-transition", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-transition", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/robert<PERSON>a"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-transition"}, "scripts": {}, "dependencies": {"@types/d3-selection": "*"}, "typesPublisherContentHash": "69cd5510811e76132548e17770c16a10a8ffe039c97e6d45b3663749af4591dd", "typeScriptVersion": "4.8"}