import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useData } from '../contexts/DataContext';
import './FileUpload.css';

const FileUpload = () => {
  const { parseCSVFile, loading, error } = useData();

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      try {
        await parseCSVFile(file);
      } catch (error) {
        console.error('Error parsing file:', error);
      }
    }
  }, [parseCSVFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    multiple: false
  });

  return (
    <div className="file-upload-container">
      <div
        {...getRootProps()}
        className={`dropzone ${isDragActive ? 'active' : ''} ${loading ? 'loading' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="dropzone-content">
          {loading ? (
            <div className="loading-spinner">
              <div className="spinner"></div>
              <p>Processing file...</p>
            </div>
          ) : isDragActive ? (
            <div className="drag-active">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <polyline points="7,10 12,15 17,10" />
                <line x1="12" y1="15" x2="12" y2="3" />
              </svg>
              <p>Drop your CSV file here</p>
            </div>
          ) : (
            <div className="drag-inactive">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <polyline points="14,2 14,8 20,8" />
                <line x1="16" y1="13" x2="8" y2="13" />
                <line x1="16" y1="17" x2="8" y2="17" />
                <polyline points="10,9 9,9 8,9" />
              </svg>
              <p>Drag & drop a CSV file here, or click to select</p>
              <span className="file-types">Supported: .csv files</span>
            </div>
          )}
        </div>
      </div>
      
      {error && (
        <div className="error-message">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10" />
            <line x1="15" y1="9" x2="9" y2="15" />
            <line x1="9" y1="9" x2="15" y2="15" />
          </svg>
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
