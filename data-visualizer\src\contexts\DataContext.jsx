import React, { createContext, useContext, useReducer } from 'react';
import <PERSON> from 'papaparse';

const DataContext = createContext();

const initialState = {
  datasets: [],
  currentDataset: null,
  loading: false,
  error: null,
  apiConnections: [],
  streamingData: null
};

const dataReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'ADD_DATASET':
      return {
        ...state,
        datasets: [...state.datasets, action.payload],
        currentDataset: action.payload,
        loading: false,
        error: null
      };
    
    case 'SET_CURRENT_DATASET':
      return { ...state, currentDataset: action.payload };
    
    case 'UPDATE_DATASET':
      return {
        ...state,
        datasets: state.datasets.map(dataset =>
          dataset.id === action.payload.id ? action.payload : dataset
        ),
        currentDataset: action.payload
      };
    
    case 'REMOVE_DATASET':
      const filteredDatasets = state.datasets.filter(d => d.id !== action.payload);
      return {
        ...state,
        datasets: filteredDatasets,
        currentDataset: filteredDatasets.length > 0 ? filteredDatasets[0] : null
      };
    
    case 'ADD_API_CONNECTION':
      return {
        ...state,
        apiConnections: [...state.apiConnections, action.payload]
      };
    
    case 'UPDATE_STREAMING_DATA':
      return { ...state, streamingData: action.payload };
    
    default:
      return state;
  }
};

export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  const parseCSVFile = (file) => {
    return new Promise((resolve, reject) => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: true,
        complete: (results) => {
          if (results.errors.length > 0) {
            dispatch({ type: 'SET_ERROR', payload: results.errors[0].message });
            reject(results.errors[0]);
          } else {
            const dataset = {
              id: Date.now().toString(),
              name: file.name.replace('.csv', ''),
              data: results.data,
              columns: results.meta.fields,
              type: 'csv',
              createdAt: new Date().toISOString()
            };
            dispatch({ type: 'ADD_DATASET', payload: dataset });
            resolve(dataset);
          }
        },
        error: (error) => {
          dispatch({ type: 'SET_ERROR', payload: error.message });
          reject(error);
        }
      });
    });
  };

  const addDataset = (dataset) => {
    dispatch({ type: 'ADD_DATASET', payload: dataset });
  };

  const setCurrentDataset = (dataset) => {
    dispatch({ type: 'SET_CURRENT_DATASET', payload: dataset });
  };

  const updateDataset = (dataset) => {
    dispatch({ type: 'UPDATE_DATASET', payload: dataset });
  };

  const removeDataset = (datasetId) => {
    dispatch({ type: 'REMOVE_DATASET', payload: datasetId });
  };

  const clearError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  const connectToAPI = async (apiConfig) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await fetch(apiConfig.url, {
        method: apiConfig.method || 'GET',
        headers: apiConfig.headers || {},
      });
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      const dataset = {
        id: Date.now().toString(),
        name: apiConfig.name || 'API Data',
        data: Array.isArray(data) ? data : [data],
        columns: data.length > 0 ? Object.keys(data[0]) : [],
        type: 'api',
        apiConfig,
        createdAt: new Date().toISOString()
      };
      
      dispatch({ type: 'ADD_DATASET', payload: dataset });
      dispatch({ type: 'ADD_API_CONNECTION', payload: apiConfig });
      
      return dataset;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    }
  };

  const value = {
    ...state,
    parseCSVFile,
    addDataset,
    setCurrentDataset,
    updateDataset,
    removeDataset,
    clearError,
    connectToAPI
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
