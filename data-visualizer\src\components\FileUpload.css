.file-upload-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.dropzone {
  border: 2px dashed #e1e5e9;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropzone:hover {
  border-color: #4f46e5;
  background: #f8faff;
}

.dropzone.active {
  border-color: #4f46e5;
  background: #f0f4ff;
  transform: scale(1.02);
}

.dropzone.loading {
  border-color: #6b7280;
  background: #f9fafb;
  cursor: not-allowed;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.drag-active svg,
.drag-inactive svg {
  color: #6b7280;
  margin-bottom: 8px;
}

.dropzone.active .drag-active svg {
  color: #4f46e5;
}

.dropzone:hover .drag-inactive svg {
  color: #4f46e5;
}

.drag-active p,
.drag-inactive p {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.file-types {
  font-size: 14px;
  color: #6b7280;
  margin-top: 8px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
}

.error-message svg {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

@media (max-width: 640px) {
  .dropzone {
    padding: 30px 15px;
    min-height: 150px;
  }
  
  .drag-active p,
  .drag-inactive p {
    font-size: 16px;
  }
  
  .file-types {
    font-size: 12px;
  }
}
