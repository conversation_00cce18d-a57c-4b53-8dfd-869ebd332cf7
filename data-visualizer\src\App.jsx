import React, { useState } from 'react';
import { DataProvider } from './contexts/DataContext';
import FileUpload from './components/FileUpload';
import DataPreview from './components/DataPreview';
import ChartBuilder from './components/ChartBuilder';
import ApiConnection from './components/ApiConnection';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('upload');

  return (
    <DataProvider>
      <div className="app">
        <header className="app-header">
          <div className="header-content">
            <h1>Interactive Data Visualizer</h1>
            <p>Upload CSV files or connect to APIs to create beautiful, interactive charts</p>
          </div>
        </header>

        <nav className="app-nav">
          <button
            className={`nav-btn ${activeTab === 'upload' ? 'active' : ''}`}
            onClick={() => setActiveTab('upload')}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7,10 12,15 17,10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            Upload Data
          </button>
          <button
            className={`nav-btn ${activeTab === 'preview' ? 'active' : ''}`}
            onClick={() => setActiveTab('preview')}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
              <line x1="9" y1="9" x2="15" y2="9" />
              <line x1="9" y1="15" x2="15" y2="15" />
            </svg>
            Data Preview
          </button>
          <button
            className={`nav-btn ${activeTab === 'visualize' ? 'active' : ''}`}
            onClick={() => setActiveTab('visualize')}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="20" x2="18" y2="10" />
              <line x1="12" y1="20" x2="12" y2="4" />
              <line x1="6" y1="20" x2="6" y2="14" />
            </svg>
            Visualize
          </button>
        </nav>

        <main className="app-main">
          <div className="main-content">
            {activeTab === 'upload' && (
              <div className="tab-content">
                <h2>Upload Your Data</h2>
                <FileUpload />
              </div>
            )}

            {activeTab === 'preview' && (
              <div className="tab-content">
                <h2>Data Preview</h2>
                <DataPreview />
              </div>
            )}

            {activeTab === 'visualize' && (
              <div className="tab-content">
                <h2>Create Visualizations</h2>
                <ChartBuilder />
              </div>
            )}
          </div>
        </main>
      </div>
    </DataProvider>
  );
}

export default App;
