Month,Sales,Expenses,Profit,Region,Product
January,12000,8000,4000,North,<PERSON>ptops
February,15000,9000,6000,North,Laptops
March,18000,10000,8000,North,Laptops
April,16000,9500,6500,North,Laptops
May,20000,11000,9000,North,Laptops
June,22000,12000,10000,North,Laptops
January,8000,5000,3000,South,Tablets
February,9500,5500,4000,South,Tablets
March,11000,6000,5000,South,Tablets
April,10500,6200,4300,South,Tablets
May,13000,7000,6000,South,Tablets
June,14500,7500,7000,South,Tablets
January,6000,4000,2000,East,Phones
February,7200,4500,2700,East,Phones
March,8500,5000,3500,East,Phones
April,9000,5200,3800,East,Phones
May,10500,5800,4700,East,Phones
June,12000,6500,5500,East,Phones
January,9500,6000,3500,West,Accessories
February,11000,6800,4200,West,Accessories
March,12500,7200,5300,West,Accessories
April,11800,7000,4800,West,Accessories
May,14000,8000,6000,West,Accessories
June,15500,8500,7000,West,Accessories
