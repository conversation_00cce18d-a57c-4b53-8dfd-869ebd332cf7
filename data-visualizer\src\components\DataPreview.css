.data-preview {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.data-preview-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
}

.data-preview-empty svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.data-preview-empty p {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.data-preview-empty span {
  font-size: 14px;
  color: #6b7280;
}

.data-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.dataset-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #111827;
}

.dataset-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.data-count,
.column-count {
  color: #6b7280;
}

.data-type {
  background: #dbeafe;
  color: #1e40af;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
}

.dataset-selector select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.remove-dataset-btn {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-dataset-btn:hover {
  background: #fecaca;
}

.columns-overview {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.columns-overview h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.columns-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.column-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f3f4f6;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.column-name {
  font-weight: 500;
  color: #374151;
}

.column-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.column-type.number {
  background: #dcfce7;
  color: #166534;
}

.column-type.date {
  background: #fef3c7;
  color: #92400e;
}

.column-type.text {
  background: #e0e7ff;
  color: #3730a3;
}

.data-table-container {
  padding: 20px 24px;
}

.data-table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.data-table-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.toggle-view-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-view-btn:hover {
  background: #e5e7eb;
}

.data-table-wrapper {
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background: #f9fafb;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.data-table tbody tr:hover {
  background: #f9fafb;
}

.table-footer {
  text-align: center;
  padding: 12px;
  color: #6b7280;
  font-size: 14px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  margin-top: -1px;
}

@media (max-width: 768px) {
  .data-preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .dataset-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .columns-list {
    flex-direction: column;
  }
  
  .data-table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
