import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';

const PieChart = ({ 
  data, 
  width = 400, 
  height = 400, 
  margin = { top: 20, right: 20, bottom: 20, left: 20 },
  labelColumn,
  valueColumn,
  colorScheme = 'category10',
  onDataPointClick,
  className = '',
  showLabels = true,
  showLegend = true
}) => {
  const svgRef = useRef();
  const containerRef = useRef();

  useEffect(() => {
    if (!data || !data.length || !labelColumn || !valueColumn) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous chart

    const containerWidth = containerRef.current?.offsetWidth || width;
    const actualWidth = Math.min(containerWidth, width);
    const actualHeight = height;

    svg
      .attr("width", actualWidth)
      .attr("height", actualHeight);

    const innerWidth = actualWidth - margin.left - margin.right;
    const innerHeight = actualHeight - margin.top - margin.bottom;

    const radius = Math.min(innerWidth, innerHeight) / 2;
    const centerX = actualWidth / 2;
    const centerY = actualHeight / 2;

    const g = svg.append("g")
      .attr("transform", `translate(${centerX},${centerY})`);

    // Process data
    const processedData = data.filter(d => 
      d[labelColumn] !== null && d[labelColumn] !== undefined &&
      d[valueColumn] !== null && d[valueColumn] !== undefined &&
      !isNaN(+d[valueColumn])
    );

    if (processedData.length === 0) {
      svg.append("text")
        .attr("x", centerX)
        .attr("y", centerY)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("fill", "#6b7280")
        .text("No valid data to display");
      return;
    }

    // Aggregate data by label (in case there are duplicates)
    const aggregatedData = d3.rollup(
      processedData,
      v => d3.sum(v, d => +d[valueColumn]),
      d => d[labelColumn]
    );

    const pieData = Array.from(aggregatedData, ([label, value]) => ({ label, value }));

    // Set up color scale
    const colorScale = d3.scaleOrdinal(d3[`scheme${colorScheme}`] || d3.schemeCategory10);

    // Create pie layout
    const pie = d3.pie()
      .value(d => d.value)
      .sort(null);

    const arc = d3.arc()
      .innerRadius(0)
      .outerRadius(radius - 10);

    const labelArc = d3.arc()
      .innerRadius(radius - 40)
      .outerRadius(radius - 40);

    // Create pie slices
    const arcs = g.selectAll(".arc")
      .data(pie(pieData))
      .enter().append("g")
      .attr("class", "arc");

    // Add pie slices
    arcs.append("path")
      .attr("d", arc)
      .attr("fill", (d, i) => colorScale(i))
      .attr("stroke", "white")
      .attr("stroke-width", 2)
      .style("cursor", onDataPointClick ? "pointer" : "default")
      .on("click", onDataPointClick ? (event, d) => onDataPointClick(d.data) : null)
      .on("mouseover", function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("transform", "scale(1.05)");
        
        // Show tooltip
        const tooltip = g.append("g").attr("class", "tooltip");
        const rect = tooltip.append("rect")
          .attr("fill", "rgba(0, 0, 0, 0.8)")
          .attr("rx", 4);
        
        const text = tooltip.append("text")
          .attr("fill", "white")
          .attr("font-size", "12px")
          .attr("text-anchor", "middle");
        
        text.append("tspan")
          .attr("x", 0)
          .attr("dy", "1.2em")
          .text(`${d.data.label}`);
        
        text.append("tspan")
          .attr("x", 0)
          .attr("dy", "1.2em")
          .text(`Value: ${d.data.value}`);
        
        const percentage = ((d.endAngle - d.startAngle) / (2 * Math.PI) * 100).toFixed(1);
        text.append("tspan")
          .attr("x", 0)
          .attr("dy", "1.2em")
          .text(`${percentage}%`);
        
        const bbox = text.node().getBBox();
        rect.attr("x", bbox.x - 8)
          .attr("y", bbox.y - 4)
          .attr("width", bbox.width + 16)
          .attr("height", bbox.height + 8);
      })
      .on("mouseout", function() {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("transform", "scale(1)");
        
        g.select(".tooltip").remove();
      });

    // Add labels if enabled
    if (showLabels) {
      arcs.append("text")
        .attr("transform", d => `translate(${labelArc.centroid(d)})`)
        .attr("text-anchor", "middle")
        .style("font-size", "12px")
        .style("font-weight", "500")
        .style("fill", "#374151")
        .text(d => {
          const percentage = ((d.endAngle - d.startAngle) / (2 * Math.PI) * 100);
          return percentage > 5 ? `${percentage.toFixed(1)}%` : '';
        });
    }

    // Add legend if enabled
    if (showLegend) {
      const legend = svg.append("g")
        .attr("class", "legend")
        .attr("transform", `translate(${actualWidth - 150}, 20)`);

      const legendItems = legend.selectAll(".legend-item")
        .data(pieData)
        .enter().append("g")
        .attr("class", "legend-item")
        .attr("transform", (d, i) => `translate(0, ${i * 20})`);

      legendItems.append("rect")
        .attr("width", 12)
        .attr("height", 12)
        .attr("fill", (d, i) => colorScale(i));

      legendItems.append("text")
        .attr("x", 18)
        .attr("y", 6)
        .attr("dy", "0.35em")
        .style("font-size", "12px")
        .style("fill", "#374151")
        .text(d => {
          const label = d.label.toString();
          return label.length > 15 ? label.substring(0, 15) + '...' : label;
        });
    }

  }, [data, width, height, margin, labelColumn, valueColumn, colorScheme, onDataPointClick, showLabels, showLegend]);

  return (
    <div ref={containerRef} className={`chart-container pie-chart ${className}`}>
      <svg ref={svgRef}></svg>
    </div>
  );
};

export default PieChart;
