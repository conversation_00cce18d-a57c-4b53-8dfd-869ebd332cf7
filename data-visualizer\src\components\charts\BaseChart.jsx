import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';

const BaseChart = ({ 
  data, 
  width = 800, 
  height = 400, 
  margin = { top: 20, right: 30, bottom: 40, left: 50 },
  chartType = 'bar',
  xColumn,
  yColumn,
  colorScheme = 'category10',
  onDataPointClick,
  className = ''
}) => {
  const svgRef = useRef();
  const containerRef = useRef();

  useEffect(() => {
    if (!data || !data.length || !xColumn || !yColumn) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous chart

    const containerWidth = containerRef.current?.offsetWidth || width;
    const actualWidth = Math.min(containerWidth, width);
    const actualHeight = height;

    svg
      .attr("width", actualWidth)
      .attr("height", actualHeight);

    const innerWidth = actualWidth - margin.left - margin.right;
    const innerHeight = actualHeight - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Process data
    const processedData = data.filter(d => 
      d[xColumn] !== null && d[xColumn] !== undefined &&
      d[yColumn] !== null && d[yColumn] !== undefined
    );

    if (processedData.length === 0) {
      g.append("text")
        .attr("x", innerWidth / 2)
        .attr("y", innerHeight / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("fill", "#6b7280")
        .text("No valid data to display");
      return;
    }

    // Set up scales
    let xScale, yScale;
    const colorScale = d3.scaleOrdinal(d3[`scheme${colorScheme}`] || d3.schemeCategory10);

    // Determine if x-axis data is numeric or categorical
    const xValues = processedData.map(d => d[xColumn]);
    const isXNumeric = xValues.every(val => !isNaN(val) && val !== '');
    
    if (isXNumeric) {
      xScale = d3.scaleLinear()
        .domain(d3.extent(processedData, d => +d[xColumn]))
        .range([0, innerWidth]);
    } else {
      xScale = d3.scaleBand()
        .domain(processedData.map(d => d[xColumn]))
        .range([0, innerWidth])
        .padding(0.1);
    }

    // Y-scale is typically numeric
    const yExtent = d3.extent(processedData, d => +d[yColumn]);
    yScale = d3.scaleLinear()
      .domain([0, yExtent[1] * 1.1]) // Add 10% padding to top
      .range([innerHeight, 0]);

    // Create axes
    const xAxis = d3.axisBottom(xScale);
    const yAxis = d3.axisLeft(yScale);

    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll("text")
      .style("font-size", "12px")
      .style("fill", "#6b7280");

    g.append("g")
      .attr("class", "y-axis")
      .call(yAxis)
      .selectAll("text")
      .style("font-size", "12px")
      .style("fill", "#6b7280");

    // Add axis labels
    g.append("text")
      .attr("class", "x-label")
      .attr("text-anchor", "middle")
      .attr("x", innerWidth / 2)
      .attr("y", innerHeight + margin.bottom - 5)
      .style("font-size", "14px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text(xColumn);

    g.append("text")
      .attr("class", "y-label")
      .attr("text-anchor", "middle")
      .attr("transform", "rotate(-90)")
      .attr("x", -innerHeight / 2)
      .attr("y", -margin.left + 15)
      .style("font-size", "14px")
      .style("font-weight", "500")
      .style("fill", "#374151")
      .text(yColumn);

    // Render chart based on type
    switch (chartType) {
      case 'bar':
        renderBarChart(g, processedData, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick);
        break;
      case 'line':
        renderLineChart(g, processedData, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick);
        break;
      case 'scatter':
        renderScatterPlot(g, processedData, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick);
        break;
      case 'area':
        renderAreaChart(g, processedData, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick);
        break;
      default:
        renderBarChart(g, processedData, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick);
    }

  }, [data, width, height, margin, chartType, xColumn, yColumn, colorScheme, onDataPointClick]);

  return (
    <div ref={containerRef} className={`chart-container ${className}`}>
      <svg ref={svgRef}></svg>
    </div>
  );
};

// Chart rendering functions
const renderBarChart = (g, data, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick) => {
  const bars = g.selectAll(".bar")
    .data(data)
    .enter().append("rect")
    .attr("class", "bar")
    .attr("x", d => xScale(d[xColumn]))
    .attr("width", xScale.bandwidth ? xScale.bandwidth() : 8)
    .attr("y", d => yScale(+d[yColumn]))
    .attr("height", d => yScale(0) - yScale(+d[yColumn]))
    .attr("fill", (d, i) => colorScale(i))
    .style("cursor", onDataPointClick ? "pointer" : "default")
    .on("click", onDataPointClick ? (event, d) => onDataPointClick(d) : null);

  // Add hover effects
  bars.on("mouseover", function(event, d) {
    d3.select(this).style("opacity", 0.8);
    
    // Tooltip
    const tooltip = g.append("g").attr("class", "tooltip");
    const rect = tooltip.append("rect")
      .attr("fill", "rgba(0, 0, 0, 0.8)")
      .attr("rx", 4);
    
    const text = tooltip.append("text")
      .attr("fill", "white")
      .attr("font-size", "12px")
      .attr("text-anchor", "middle");
    
    text.append("tspan")
      .attr("x", 0)
      .attr("dy", "1.2em")
      .text(`${xColumn}: ${d[xColumn]}`);
    
    text.append("tspan")
      .attr("x", 0)
      .attr("dy", "1.2em")
      .text(`${yColumn}: ${d[yColumn]}`);
    
    const bbox = text.node().getBBox();
    rect.attr("x", bbox.x - 8)
      .attr("y", bbox.y - 4)
      .attr("width", bbox.width + 16)
      .attr("height", bbox.height + 8);
    
    const x = xScale(d[xColumn]) + (xScale.bandwidth ? xScale.bandwidth() / 2 : 4);
    const y = yScale(+d[yColumn]) - 10;
    tooltip.attr("transform", `translate(${x}, ${y})`);
  })
  .on("mouseout", function() {
    d3.select(this).style("opacity", 1);
    g.select(".tooltip").remove();
  });
};

const renderLineChart = (g, data, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick) => {
  const line = d3.line()
    .x(d => xScale(d[xColumn]))
    .y(d => yScale(+d[yColumn]))
    .curve(d3.curveMonotoneX);

  g.append("path")
    .datum(data)
    .attr("class", "line")
    .attr("fill", "none")
    .attr("stroke", colorScale(0))
    .attr("stroke-width", 2)
    .attr("d", line);

  // Add data points
  g.selectAll(".dot")
    .data(data)
    .enter().append("circle")
    .attr("class", "dot")
    .attr("cx", d => xScale(d[xColumn]))
    .attr("cy", d => yScale(+d[yColumn]))
    .attr("r", 4)
    .attr("fill", colorScale(0))
    .style("cursor", onDataPointClick ? "pointer" : "default")
    .on("click", onDataPointClick ? (event, d) => onDataPointClick(d) : null);
};

const renderScatterPlot = (g, data, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick) => {
  g.selectAll(".dot")
    .data(data)
    .enter().append("circle")
    .attr("class", "dot")
    .attr("cx", d => xScale(d[xColumn]))
    .attr("cy", d => yScale(+d[yColumn]))
    .attr("r", 5)
    .attr("fill", (d, i) => colorScale(i))
    .attr("opacity", 0.7)
    .style("cursor", onDataPointClick ? "pointer" : "default")
    .on("click", onDataPointClick ? (event, d) => onDataPointClick(d) : null);
};

const renderAreaChart = (g, data, xScale, yScale, colorScale, xColumn, yColumn, onDataPointClick) => {
  const area = d3.area()
    .x(d => xScale(d[xColumn]))
    .y0(yScale(0))
    .y1(d => yScale(+d[yColumn]))
    .curve(d3.curveMonotoneX);

  g.append("path")
    .datum(data)
    .attr("class", "area")
    .attr("fill", colorScale(0))
    .attr("opacity", 0.6)
    .attr("d", area);

  // Add line on top
  const line = d3.line()
    .x(d => xScale(d[xColumn]))
    .y(d => yScale(+d[yColumn]))
    .curve(d3.curveMonotoneX);

  g.append("path")
    .datum(data)
    .attr("class", "line")
    .attr("fill", "none")
    .attr("stroke", colorScale(0))
    .attr("stroke-width", 2)
    .attr("d", line);
};

export default BaseChart;
